import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  RefreshCw, 
  Download, 
  Search, 
  Filter,
  DollarSign,
  AlertCircle,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';
import { CancellationWithDetails, CancellationFilters, CancellationStats } from '@/types/cancellation';
import CancellationsList from '@/components/admin/CancellationsList';
import RefundStatusModal from '@/components/admin/RefundStatusModal';

const RefundsCancellations: React.FC = () => {
  const { user, userRole } = useAuth();
  const [cancellations, setCancellations] = useState<CancellationWithDetails[]>([]);
  const [stats, setStats] = useState<CancellationStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<CancellationFilters>({
    refund_status: 'all',
    cancelled_by_role: 'all',
    search_query: ''
  });
  const [showRefundModal, setShowRefundModal] = useState(false);
  const [selectedCancellation, setSelectedCancellation] = useState<CancellationWithDetails | null>(null);

  // Only super_admin can access this page
  if (userRole !== 'super_admin') {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">Only super administrators can access refunds and cancellations.</p>
        </div>
      </div>
    );
  }

  useEffect(() => {
    fetchCancellations();
    fetchStats();
  }, [filters]);

  const fetchCancellations = async () => {
    try {
      setLoading(true);
      
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      // Call the database function to get cancellations with details
      const { data, error } = await supabase.rpc('get_cancellations_with_details', {
        admin_user_id: user.id,
        limit_count: 100,
        offset_count: 0
      });

      if (error) throw error;

      let filteredData = data || [];

      // Apply client-side filters
      if (filters.refund_status && filters.refund_status !== 'all') {
        filteredData = filteredData.filter(c => c.refund_status === filters.refund_status);
      }

      if (filters.cancelled_by_role && filters.cancelled_by_role !== 'all') {
        filteredData = filteredData.filter(c => c.cancelled_by_role === filters.cancelled_by_role);
      }

      if (filters.search_query) {
        const query = filters.search_query.toLowerCase();
        filteredData = filteredData.filter(c => 
          c.customer_name.toLowerCase().includes(query) ||
          c.venue_name.toLowerCase().includes(query) ||
          c.court_name.toLowerCase().includes(query) ||
          c.cancellation_reason.toLowerCase().includes(query)
        );
      }

      setCancellations(filteredData);
    } catch (error) {
      console.error('Error fetching cancellations:', error);
      toast({
        title: 'Error',
        description: 'Failed to load cancellations data',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Calculate stats from cancellations data
      const totalCancellations = cancellations.length;
      const pendingRefunds = cancellations.filter(c => c.refund_status === 'pending').length;
      const processedRefunds = cancellations.filter(c => c.refund_status === 'processed').length;
      const totalRefundAmount = cancellations.reduce((sum, c) => sum + (c.refund_amount || 0), 0);
      const avgRefundAmount = totalCancellations > 0 ? totalRefundAmount / totalCancellations : 0;
      
      const cancellationsByRole = {
        admin: cancellations.filter(c => c.cancelled_by_role === 'admin').length,
        super_admin: cancellations.filter(c => c.cancelled_by_role === 'super_admin').length
      };

      setStats({
        total_cancellations: totalCancellations,
        pending_refunds: pendingRefunds,
        processed_refunds: processedRefunds,
        total_refund_amount: totalRefundAmount,
        avg_refund_amount: avgRefundAmount,
        cancellations_by_role: cancellationsByRole
      });
    } catch (error) {
      console.error('Error calculating stats:', error);
    }
  };

  const handleRefundStatusUpdate = async (cancellationId: string, newStatus: string, notes?: string, razorpayRefundId?: string) => {
    try {
      const { data, error } = await supabase.rpc('update_refund_status', {
        cancellation_id: cancellationId,
        new_status: newStatus,
        refund_notes: notes,
        razorpay_refund_id: razorpayRefundId
      });

      if (error) throw error;

      toast({
        title: 'Refund Status Updated',
        description: `Refund status has been updated to ${newStatus}`,
      });

      // Refresh data
      fetchCancellations();
      setShowRefundModal(false);
      setSelectedCancellation(null);
    } catch (error) {
      console.error('Error updating refund status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update refund status',
        variant: 'destructive',
      });
    }
  };

  const exportToCsv = () => {
    const csvData = cancellations.map(c => ({
      'Cancellation Date': new Date(c.cancelled_at).toLocaleDateString(),
      'Booking Date': c.booking_date,
      'Venue': c.venue_name,
      'Court': c.court_name,
      'Customer': c.customer_name,
      'Amount': c.total_price,
      'Refund Amount': c.refund_amount || 0,
      'Refund Status': c.refund_status,
      'Cancelled By': c.cancelled_by_role,
      'Reason': c.cancellation_reason,
      'Payment Reference': c.payment_reference || ''
    }));

    const csv = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).map(val => `"${val}"`).join(','))
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `cancellations-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Refunds & Cancellations</h1>
          <p className="text-gray-600">Manage booking cancellations and process refunds</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={fetchCancellations} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={exportToCsv} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Cancellations</p>
                  <p className="text-2xl font-bold">{stats.total_cancellations}</p>
                </div>
                <AlertCircle className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Pending Refunds</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.pending_refunds}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Processed Refunds</p>
                  <p className="text-2xl font-bold text-green-600">{stats.processed_refunds}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Refund Amount</p>
                  <p className="text-2xl font-bold">₹{stats.total_refund_amount.toLocaleString()}</p>
                </div>
                <DollarSign className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Customer, venue, court..."
                  value={filters.search_query || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, search_query: e.target.value }))}
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Refund Status</label>
              <Select
                value={filters.refund_status || 'all'}
                onValueChange={(value) => setFilters(prev => ({ ...prev, refund_status: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="processed">Processed</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="not_applicable">Not Applicable</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Cancelled By</label>
              <Select
                value={filters.cancelled_by_role || 'all'}
                onValueChange={(value) => setFilters(prev => ({ ...prev, cancelled_by_role: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="admin">Venue Admin</SelectItem>
                  <SelectItem value="super_admin">Super Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button 
                onClick={() => setFilters({ refund_status: 'all', cancelled_by_role: 'all', search_query: '' })}
                variant="outline"
                className="w-full"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Cancellations List */}
      <CancellationsList
        cancellations={cancellations}
        loading={loading}
        onRefundStatusUpdate={(cancellation) => {
          setSelectedCancellation(cancellation);
          setShowRefundModal(true);
        }}
      />

      {/* Refund Status Modal */}
      <RefundStatusModal
        isOpen={showRefundModal}
        onClose={() => {
          setShowRefundModal(false);
          setSelectedCancellation(null);
        }}
        cancellation={selectedCancellation}
        onUpdate={handleRefundStatusUpdate}
      />
    </div>
  );
};

export default RefundsCancellations;


import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Eye, EyeOff, Mail, Lock, Phone, MessageCircle, Key } from 'lucide-react';
import Header from '../components/Header';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/context/AuthContext';
import { useIsMobile } from '@/hooks/use-mobile';
import { supabase } from '@/integrations/supabase/client';
import { useSecureAuth } from '@/hooks/useSecureAuth';
import { validateEmail, validatePhone, sanitizeInput } from '@/utils/security';
import { whatsappAuthService } from '@/services/whatsappAuthService';

const Login: React.FC = () => {
  // Login method state
  const [loginMethod, setLoginMethod] = useState<'email' | 'phone'>('email');

  // Email login fields
  const [email, setEmail] = useState('');

  // Phone login fields
  const [phone, setPhone] = useState('');
  const [countryCode, setCountryCode] = useState('+91');

  // Common fields
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  // Phone OTP login fields
  const [phoneAuthMethod, setPhoneAuthMethod] = useState<'password' | 'otp'>('password');
  const [otp, setOtp] = useState('');
  const [showOtpInput, setShowOtpInput] = useState(false);
  const [otpTimer, setOtpTimer] = useState(0);

  // Password reset
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [isResetting, setIsResetting] = useState(false);

  // Form validation and loading states
  const [emailError, setEmailError] = useState('');
  const [phoneError, setPhoneError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [otpError, setOtpError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const isMobile = useIsMobile();
  const { signIn } = useAuth();
  const { secureSignIn } = useSecureAuth();

  // Start OTP timer
  const startOtpTimer = () => {
    setOtpTimer(300); // 5 minutes
    const timer = setInterval(() => {
      setOtpTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // Format timer display
  const formatTimer = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Validate email login form
  const validateEmailForm = () => {
    let isValid = true;
    setEmailError('');
    setPasswordError('');

    if (!email || !validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      isValid = false;
    }

    if (!password || password.length < 6) {
      setPasswordError('Password must be at least 6 characters long');
      isValid = false;
    }

    return isValid;
  };

  // Validate phone login form
  const validatePhoneForm = () => {
    let isValid = true;
    setPhoneError('');

    if (phoneAuthMethod === 'password') {
      setPasswordError('');
    } else {
      setOtpError('');
    }

    if (!phone || !validatePhone(countryCode + phone)) {
      setPhoneError('Please enter a valid phone number');
      isValid = false;
    }

    if (phoneAuthMethod === 'password') {
      if (!password || password.length < 6) {
        setPasswordError('Password must be at least 6 characters long');
        isValid = false;
      }
    } else if (showOtpInput) {
      if (!otp || otp.length !== 6) {
        setOtpError('Please enter a valid 6-digit OTP');
        isValid = false;
      }
    }

    return isValid;
  };

  // Handle email login
  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateEmailForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const sanitizedEmail = sanitizeInput(email);
      const { error } = await secureSignIn(sanitizedEmail, password);

      if (!error) {
        toast({
          title: "Login successful",
          description: "Welcome back to Grid2Play!",
        });
      }
    } catch (error) {
      toast({
        title: "Login failed",
        description: "Please check your credentials and try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle phone + password login
  const handlePhonePasswordLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validatePhoneForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const fullPhone = countryCode + sanitizeInput(phone);

      const result = await whatsappAuthService.signInWithPhone(fullPhone, password);

      if (result.success) {
        toast({
          title: "Login successful",
          description: "Welcome back to Grid2Play!",
        });
      } else {
        toast({
          title: "Login failed",
          description: result.error || "Please check your credentials and try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Login failed",
        description: "Please check your credentials and try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle phone + OTP login (send OTP)
  const handleSendLoginOTP = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!phone || !validatePhone(countryCode + phone)) {
      setPhoneError('Please enter a valid phone number');
      return;
    }

    setIsLoading(true);

    try {
      const fullPhone = countryCode + sanitizeInput(phone);

      const result = await whatsappAuthService.sendLoginOTP(fullPhone);

      if (result.success) {
        setShowOtpInput(true);
        startOtpTimer();
        toast({
          title: "OTP Sent!",
          description: `We've sent a login OTP to your WhatsApp number ${fullPhone}`,
        });
      } else {
        toast({
          title: "Failed to send OTP",
          description: result.error || "Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Failed to send OTP",
        description: "Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle OTP verification for login
  const handleVerifyLoginOTP = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validatePhoneForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const fullPhone = countryCode + sanitizeInput(phone);

      const result = await whatsappAuthService.verifyLoginOTP(fullPhone, otp);

      if (result.success) {
        toast({
          title: "Login successful",
          description: "Welcome back to Grid2Play!",
        });
      } else {
        toast({
          title: "OTP verification failed",
          description: result.error || "Please check your OTP and try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "OTP verification failed",
        description: "Please check your OTP and try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!resetEmail || !validateEmail(resetEmail)) {
      toast({
        title: "Invalid email",
        description: "Please enter a valid email address.",
        variant: "destructive",
      });
      return;
    }

    setIsResetting(true);

    try {
      const sanitizedEmail = sanitizeInput(resetEmail);
      const { error } = await supabase.auth.resetPasswordForEmail(sanitizedEmail, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) {
        console.error('Password reset error:', error);
        // Generic message to prevent email enumeration
        toast({
          title: "Reset request processed",
          description: "If an account with this email exists, you will receive password reset instructions.",
        });
      } else {
        toast({
          title: "Reset email sent",
          description: "Please check your email for password reset instructions. The link will redirect you to set a new password.",
        });
        setShowForgotPassword(false);
        setResetEmail('');
      }
    } catch (error: any) {
      console.error('Unexpected error:', error);
      toast({
        title: "Reset request processed",
        description: "If an account with this email exists, you will receive password reset instructions.",
      });
    } finally {
      setIsResetting(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/`
        }
      });

      if (error) {
        toast({
          title: "Google Sign-In failed",
          description: "Unable to connect with Google. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Google Sign-In failed",
        description: "Unable to connect with Google. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (showForgotPassword) {
    return (
      <div className="min-h-screen relative bg-gradient-to-br from-black via-[#1E3B2C] to-black overflow-hidden">
        {/* Floating dark green SVG accent, hidden on mobile */}
        <div className="hidden sm:block absolute -top-32 -left-32 w-[400px] h-[400px] pointer-events-none opacity-20 animate-float z-0">
          <svg viewBox="0 0 400 400" fill="none">
            <ellipse cx="200" cy="200" rx="200" ry="200" fill="#1E3B2C" />
          </svg>
        </div>
        <Header />

        <div className="pt-8 pb-4 sm:pt-24 sm:pb-16 relative z-10 w-full flex-1 flex flex-col justify-center items-center sm:block" style={{ minHeight: 'calc(100vh - 64px)' }}>
          <div className="container mx-auto px-2 sm:px-4">
            <div className="w-full mx-0 sm:max-w-md sm:mx-auto bg-black/80 border-2 border-[#1E3B2C]/60 shadow-xl sm:shadow-2xl rounded-xl sm:rounded-2xl overflow-hidden animate-fade-in backdrop-blur-lg">
              <div className="p-4 sm:p-8">
                <div className="text-center mb-6 sm:mb-8">
                  <div className="w-12 h-12 sm:w-20 sm:h-20 bg-[#1E3B2C]/30 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg">
                    <Mail className="h-6 w-6 sm:h-10 sm:w-10 text-[#2E7D32]" />
                  </div>
                  <h1 className="text-xl sm:text-3xl font-extrabold text-[#2E7D32] drop-shadow">Reset Password</h1>
                  <p className="text-gray-300 mt-1 sm:mt-2 text-sm sm:text-base">Enter your email to receive reset instructions</p>
                </div>

                <form onSubmit={handleForgotPassword} className="space-y-4 sm:space-y-6">
                  <div>
                    <label htmlFor="reset-email" className="block text-xs sm:text-sm font-medium text-[#2E7D32] mb-1">
                      Email Address
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Mail className="h-4 w-4 sm:h-5 sm:w-5 text-[#2E7D32]" />
                      </div>
                      <input
                        id="reset-email"
                        type="email"
                        value={resetEmail}
                        onChange={(e) => setResetEmail(e.target.value)}
                        className="pl-10 w-full p-2 sm:p-3 border border-[#1E3B2C]/60 bg-black/70 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-[#2E7D32] focus:border-[#2E7D32] transition-all text-sm sm:text-base"
                        placeholder="Enter your email"
                        required
                        maxLength={254}
                      />
                    </div>
                  </div>

                  <div className="space-y-3">
                    <button
                      type="submit"
                      className="w-full py-2 sm:py-3 px-4 bg-gradient-to-r from-[#1E3B2C] via-[#2E7D32] to-[#1E3B2C] text-white rounded-md font-bold shadow-md sm:shadow-lg hover:from-[#2E7D32] hover:to-[#1E3B2C] hover:shadow-[#2E7D32]/40 transition-all flex justify-center items-center transform hover:scale-[1.03] focus:outline-none focus:ring-2 focus:ring-[#2E7D32] text-sm sm:text-lg"
                      disabled={isResetting}
                    >
                      {isResetting ? (
                        <span className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Sending...
                        </span>
                      ) : (
                        'Send Reset Email'
                      )}
                    </button>

                    <button
                      type="button"
                      onClick={() => setShowForgotPassword(false)}
                      className="w-full py-2 sm:py-3 px-4 border border-[#1E3B2C]/60 text-[#2E7D32] rounded-md font-medium hover:bg-[#1E3B2C]/20 transition-all text-sm sm:text-base"
                    >
                      Back to Login
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative bg-gradient-to-br from-black via-[#1E3B2C] to-black overflow-hidden">
      {/* Floating dark green SVG accent, hidden on mobile */}
      <div className="hidden sm:block absolute -top-32 -left-32 w-[400px] h-[400px] pointer-events-none opacity-20 animate-float z-0">
        <svg viewBox="0 0 400 400" fill="none">
          <ellipse cx="200" cy="200" rx="200" ry="200" fill="#1E3B2C" />
        </svg>
      </div>
      <Header />

      <div className="pt-8 pb-4 sm:pt-24 sm:pb-16 relative z-10 w-full flex-1 flex flex-col justify-center items-center sm:block" style={{ minHeight: 'calc(100vh - 64px)' }}>
        <div className="container mx-auto px-2 sm:px-4">
          <div className="w-full mx-0 sm:max-w-md sm:mx-auto bg-black/80 border-2 border-[#1E3B2C]/60 shadow-xl sm:shadow-2xl rounded-xl sm:rounded-2xl overflow-hidden animate-fade-in backdrop-blur-lg">
            <div className="p-4 sm:p-8">
              <div className="text-center mb-6 sm:mb-8">
                <div className="w-12 h-12 sm:w-20 sm:h-20 bg-[#1E3B2C]/30 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 sm:h-10 sm:w-10 text-[#2E7D32]" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M12 2a10 10 0 1 0 0 20 10 10 0 1 0 0-20z"></path>
                    <path d="M12 8v8"></path>
                    <path d="M8 12h8"></path>
                  </svg>
                </div>
                <h1 className="text-xl sm:text-3xl font-extrabold text-[#2E7D32] drop-shadow">Welcome Back</h1>
                <p className="text-gray-300 mt-1 sm:mt-2 text-sm sm:text-base">Sign in to continue with Grid2Play</p>
              </div>

              {/* Login Method Tabs */}
              <div className="flex mb-6 bg-[#1E3B2C]/30 rounded-lg p-1">
                <button
                  type="button"
                  onClick={() => {
                    setLoginMethod('email');
                    setShowOtpInput(false);
                    setOtp('');
                  }}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-300 flex items-center justify-center gap-2 ${
                    loginMethod === 'email'
                      ? 'bg-[#2E7D32] text-white shadow-lg'
                      : 'text-gray-300 hover:text-white hover:bg-[#1E3B2C]/50'
                  }`}
                >
                  <Mail className="h-4 w-4" />
                  Email
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setLoginMethod('phone');
                    setShowOtpInput(false);
                    setOtp('');
                  }}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-300 flex items-center justify-center gap-2 ${
                    loginMethod === 'phone'
                      ? 'bg-green-600 text-white shadow-lg'
                      : 'text-gray-300 hover:text-white hover:bg-[#1E3B2C]/50'
                  }`}
                >
                  <MessageCircle className="h-4 w-4" />
                  WhatsApp
                </button>
              </div>

              {/* Email Login Form */}
              {loginMethod === 'email' && (
                <form onSubmit={handleEmailLogin} className="space-y-4 sm:space-y-6">
                  {/* Email Field */}
                  <div>
                    <label htmlFor="email-login" className="block text-xs sm:text-sm font-medium text-[#2E7D32] mb-1">
                      Email Address
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Mail className="h-4 w-4 sm:h-5 sm:w-5 text-[#2E7D32]" />
                      </div>
                      <input
                        id="email-login"
                        type="email"
                        value={email}
                        onChange={(e) => {
                          setEmail(e.target.value);
                          setEmailError('');
                        }}
                        className={`pl-10 w-full p-2 sm:p-3 border ${emailError ? 'border-red-500' : 'border-[#1E3B2C]/60'} bg-black/70 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-[#2E7D32] focus:border-[#2E7D32] transition-all text-sm sm:text-base`}
                        placeholder="Enter your email"
                        required
                        maxLength={254}
                      />
                    </div>
                    {emailError && <p className="text-red-400 text-xs mt-1">{emailError}</p>}
                  </div>

                  {/* Password Field */}
                  <div>
                    <label htmlFor="email-password" className="block text-xs sm:text-sm font-medium text-[#2E7D32] mb-1">
                      Password
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Lock className="h-4 w-4 sm:h-5 sm:w-5 text-[#2E7D32]" />
                      </div>
                      <input
                        id="email-password"
                        type={showPassword ? "text" : "password"}
                        value={password}
                        onChange={(e) => {
                          setPassword(e.target.value);
                          setPasswordError('');
                        }}
                        className={`pl-10 w-full p-2 sm:p-3 border ${passwordError ? 'border-red-500' : 'border-[#1E3B2C]/60'} bg-black/70 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-[#2E7D32] focus:border-[#2E7D32] transition-all text-sm sm:text-base`}
                        placeholder="Enter your password"
                        required
                        maxLength={128}
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="text-[#2E7D32] hover:text-white focus:outline-none"
                        >
                          {showPassword ? <EyeOff className="h-4 w-4 sm:h-5 sm:w-5" /> : <Eye className="h-4 w-4 sm:h-5 sm:w-5" />}
                        </button>
                      </div>
                    </div>
                    {passwordError && <p className="text-red-400 text-xs mt-1">{passwordError}</p>}
                  </div>

                  {/* Remember Me and Forgot Password */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <input
                        id="remember-me-email"
                        name="remember-me"
                        type="checkbox"
                        className="h-4 w-4 text-[#2E7D32] focus:ring-[#2E7D32] border-[#1E3B2C]/60 rounded bg-black/70"
                      />
                      <label htmlFor="remember-me-email" className="ml-2 block text-xs sm:text-sm text-gray-200">
                        Remember me
                      </label>
                    </div>

                    <div className="text-xs sm:text-sm">
                      <button
                        type="button"
                        onClick={() => setShowForgotPassword(true)}
                        className="font-medium text-[#2E7D32] hover:text-white transition-colors"
                      >
                        Forgot password?
                      </button>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div>
                    <button
                      type="submit"
                      className="w-full py-2 sm:py-3 px-4 bg-gradient-to-r from-[#1E3B2C] via-[#2E7D32] to-[#1E3B2C] text-white rounded-md font-bold shadow-md sm:shadow-lg hover:from-[#2E7D32] hover:to-[#1E3B2C] hover:shadow-[#2E7D32]/40 transition-all flex justify-center items-center transform hover:scale-[1.03] focus:outline-none focus:ring-2 focus:ring-[#2E7D32] text-sm sm:text-lg"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <span className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Signing In...
                        </span>
                      ) : (
                        <span className="flex items-center gap-2">
                          <Mail className="h-4 w-4" />
                          Sign In with Email
                        </span>
                      )}
                    </button>
                  </div>
                </form>
              )}

              {/* Phone Login Form */}
              {loginMethod === 'phone' && (
                <div className="space-y-4 sm:space-y-6">
                  {/* Phone Authentication Method Selector */}
                  <div className="flex bg-green-600/20 rounded-lg p-1">
                    <button
                      type="button"
                      onClick={() => {
                        setPhoneAuthMethod('password');
                        setShowOtpInput(false);
                        setOtp('');
                      }}
                      className={`flex-1 py-2 px-3 rounded-md text-xs font-medium transition-all duration-300 flex items-center justify-center gap-1 ${
                        phoneAuthMethod === 'password'
                          ? 'bg-green-600 text-white shadow-lg'
                          : 'text-gray-300 hover:text-white hover:bg-green-600/30'
                      }`}
                    >
                      <Lock className="h-3 w-3" />
                      Password
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        setPhoneAuthMethod('otp');
                        setPassword('');
                      }}
                      className={`flex-1 py-2 px-3 rounded-md text-xs font-medium transition-all duration-300 flex items-center justify-center gap-1 ${
                        phoneAuthMethod === 'otp'
                          ? 'bg-green-600 text-white shadow-lg'
                          : 'text-gray-300 hover:text-white hover:bg-green-600/30'
                      }`}
                    >
                      <Key className="h-3 w-3" />
                      OTP
                    </button>
                  </div>

                  <form onSubmit={
                    phoneAuthMethod === 'password'
                      ? handlePhonePasswordLogin
                      : showOtpInput
                        ? handleVerifyLoginOTP
                        : handleSendLoginOTP
                  } className="space-y-4 sm:space-y-6">

                    {/* Phone Number Field */}
                    <div>
                      <label htmlFor="phone-login" className="block text-xs sm:text-sm font-medium text-green-400 mb-1">
                        WhatsApp Number
                      </label>
                      <div className="flex gap-2">
                        <select
                          value={countryCode}
                          onChange={(e) => setCountryCode(e.target.value)}
                          className="w-20 p-2 sm:p-3 border border-green-500/30 bg-black/70 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 transition-all text-xs sm:text-sm"
                        >
                          <option value="+91">🇮🇳 +91</option>
                          <option value="+1">🇺🇸 +1</option>
                          <option value="+44">🇬🇧 +44</option>
                          <option value="+971">🇦🇪 +971</option>
                          <option value="+65">🇸🇬 +65</option>
                        </select>
                        <div className="relative flex-1">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <MessageCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-400" />
                          </div>
                          <input
                            id="phone-login"
                            type="tel"
                            value={phone}
                            onChange={(e) => {
                              setPhone(e.target.value);
                              setPhoneError('');
                            }}
                            className={`pl-10 w-full p-2 sm:p-3 border ${phoneError ? 'border-red-500' : 'border-green-500/30'} bg-black/70 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 transition-all text-sm sm:text-base`}
                            placeholder="Enter your WhatsApp number"
                            required
                            maxLength={15}
                          />
                        </div>
                      </div>
                      {phoneError && <p className="text-red-400 text-xs mt-1">{phoneError}</p>}
                    </div>

                    {/* Password Field (for password auth) */}
                    {phoneAuthMethod === 'password' && (
                      <div>
                        <label htmlFor="phone-password" className="block text-xs sm:text-sm font-medium text-green-400 mb-1">
                          Password
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Lock className="h-4 w-4 sm:h-5 sm:w-5 text-green-400" />
                          </div>
                          <input
                            id="phone-password"
                            type={showPassword ? "text" : "password"}
                            value={password}
                            onChange={(e) => {
                              setPassword(e.target.value);
                              setPasswordError('');
                            }}
                            className={`pl-10 w-full p-2 sm:p-3 border ${passwordError ? 'border-red-500' : 'border-green-500/30'} bg-black/70 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 transition-all text-sm sm:text-base`}
                            placeholder="Enter your password"
                            required
                            maxLength={128}
                          />
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <button
                              type="button"
                              onClick={() => setShowPassword(!showPassword)}
                              className="text-green-400 hover:text-white focus:outline-none"
                            >
                              {showPassword ? <EyeOff className="h-4 w-4 sm:h-5 sm:w-5" /> : <Eye className="h-4 w-4 sm:h-5 sm:w-5" />}
                            </button>
                          </div>
                        </div>
                        {passwordError && <p className="text-red-400 text-xs mt-1">{passwordError}</p>}
                      </div>
                    )}

                    {/* OTP Field (for OTP auth) */}
                    {phoneAuthMethod === 'otp' && showOtpInput && (
                      <div>
                        <label htmlFor="phone-otp" className="block text-xs sm:text-sm font-medium text-green-400 mb-1">
                          Enter OTP
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Key className="h-4 w-4 sm:h-5 sm:w-5 text-green-400" />
                          </div>
                          <input
                            id="phone-otp"
                            type="text"
                            value={otp}
                            onChange={(e) => {
                              const value = e.target.value.replace(/\D/g, '').slice(0, 6);
                              setOtp(value);
                              setOtpError('');
                            }}
                            className={`pl-10 w-full p-2 sm:p-3 border ${otpError ? 'border-red-500' : 'border-green-500/30'} bg-black/70 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 transition-all text-sm sm:text-base text-center text-xl tracking-widest`}
                            placeholder="000000"
                            required
                            maxLength={6}
                          />
                        </div>
                        {otpError && <p className="text-red-400 text-xs mt-1">{otpError}</p>}
                        <div className="flex justify-between items-center mt-2">
                          <p className="text-green-400 text-xs">
                            📱 OTP sent to {countryCode}{phone}
                          </p>
                          {otpTimer > 0 ? (
                            <p className="text-gray-400 text-xs">
                              Resend in {formatTimer(otpTimer)}
                            </p>
                          ) : (
                            <button
                              type="button"
                              onClick={() => {
                                setShowOtpInput(false);
                                setOtp('');
                              }}
                              className="text-green-400 text-xs hover:text-green-300 transition-colors"
                            >
                              Change Number
                            </button>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Submit Button */}
                    <div>
                      <button
                        type="submit"
                        className="w-full py-2 sm:py-3 px-4 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white rounded-md font-bold shadow-md sm:shadow-lg transition-all flex justify-center items-center transform hover:scale-[1.03] focus:outline-none focus:ring-2 focus:ring-green-400 text-sm sm:text-lg"
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <span className="flex items-center">
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            {phoneAuthMethod === 'password'
                              ? 'Signing In...'
                              : showOtpInput
                                ? 'Verifying OTP...'
                                : 'Sending OTP...'}
                          </span>
                        ) : (
                          <span className="flex items-center gap-2">
                            {phoneAuthMethod === 'password' ? (
                              <>
                                <Lock className="h-4 w-4" />
                                Sign In with Password
                              </>
                            ) : showOtpInput ? (
                              <>
                                <Key className="h-4 w-4" />
                                Verify OTP
                              </>
                            ) : (
                              <>
                                <MessageCircle className="h-4 w-4" />
                                Send WhatsApp OTP
                              </>
                            )}
                          </span>
                        )}
                      </button>
                    </div>
                  </form>
                </div>
              )}

              <div className="my-4 sm:my-6 text-center">
                <p className="text-gray-400 mb-2 text-xs sm:text-base">or</p>
                <button
                  onClick={handleGoogleSignIn}
                  className="w-full py-2 sm:py-3 px-4 bg-white/90 text-black font-medium rounded-md hover:bg-[#1E3B2C]/90 hover:text-white transition-all flex items-center justify-center gap-2 transform hover:scale-[1.03] shadow-md sm:shadow-lg text-sm sm:text-base"
                >
                  <img src="https://www.svgrepo.com/show/475656/google-color.svg" alt="Google" className="w-5 h-5" />
                  Continue with Google
                </button>
              </div>

              <div className="mt-4 sm:mt-6 text-center">
                <p className="text-gray-300 text-xs sm:text-base">
                  Don't have an account?{' '}
                  <Link to="/register" className="text-[#2E7D32] hover:text-white font-medium transition-colors">
                    Sign up
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
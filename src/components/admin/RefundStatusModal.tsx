import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogFooter 
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { 
  DollarSign, 
  FileText, 
  AlertCircle,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';
import { CancellationWithDetails } from '@/types/cancellation';

interface RefundStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  cancellation: CancellationWithDetails | null;
  onUpdate: (cancellationId: string, newStatus: string, notes?: string, razorpayRefundId?: string) => Promise<void>;
}

const RefundStatusModal: React.FC<RefundStatusModalProps> = ({
  isOpen,
  onClose,
  cancellation,
  onUpdate
}) => {
  const [newStatus, setNewStatus] = useState('');
  const [refundNotes, setRefundNotes] = useState('');
  const [razorpayRefundId, setRazorpayRefundId] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (cancellation) {
      setNewStatus(cancellation.refund_status);
      setRefundNotes('');
      setRazorpayRefundId('');
      setError('');
    }
  }, [cancellation]);

  const handleSubmit = async () => {
    if (!cancellation) return;

    // Validation
    if (!newStatus) {
      setError('Please select a refund status');
      return;
    }

    if (newStatus === 'processed' && !razorpayRefundId.trim()) {
      setError('Razorpay Refund ID is required for processed refunds');
      return;
    }

    if (newStatus === 'rejected' && !refundNotes.trim()) {
      setError('Please provide a reason for rejecting the refund');
      return;
    }

    setLoading(true);
    setError('');

    try {
      await onUpdate(
        cancellation.cancellation_id,
        newStatus,
        refundNotes.trim() || undefined,
        razorpayRefundId.trim() || undefined
      );
    } catch (error) {
      console.error('Error updating refund status:', error);
      setError('Failed to update refund status. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setNewStatus('');
      setRefundNotes('');
      setRazorpayRefundId('');
      setError('');
      onClose();
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'processed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'not_applicable':
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'processed':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Processed</Badge>;
      case 'rejected':
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Rejected</Badge>;
      case 'not_applicable':
        return <Badge variant="secondary" className="bg-gray-100 text-gray-800">Not Applicable</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (!cancellation) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5 text-blue-600" />
            Update Refund Status
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Booking Summary */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">Booking Summary</h4>
            <div className="space-y-1 text-sm">
              <div><span className="text-gray-500">Customer:</span> {cancellation.customer_name}</div>
              <div><span className="text-gray-500">Venue:</span> {cancellation.venue_name} - {cancellation.court_name}</div>
              <div><span className="text-gray-500">Date:</span> {cancellation.booking_date}</div>
              <div><span className="text-gray-500">Amount:</span> ₹{cancellation.total_price.toLocaleString()}</div>
            </div>
          </div>

          {/* Current Status */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Current Status:</span>
            {getStatusBadge(cancellation.refund_status)}
          </div>

          {/* New Status Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              New Refund Status *
            </label>
            <Select value={newStatus} onValueChange={setNewStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-yellow-500" />
                    Pending
                  </div>
                </SelectItem>
                <SelectItem value="processed">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    Processed
                  </div>
                </SelectItem>
                <SelectItem value="rejected">
                  <div className="flex items-center gap-2">
                    <XCircle className="h-4 w-4 text-red-500" />
                    Rejected
                  </div>
                </SelectItem>
                <SelectItem value="not_applicable">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-gray-500" />
                    Not Applicable
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Razorpay Refund ID (for processed status) */}
          {newStatus === 'processed' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Razorpay Refund ID *
              </label>
              <Input
                value={razorpayRefundId}
                onChange={(e) => setRazorpayRefundId(e.target.value)}
                placeholder="rfnd_xxxxxxxxxx"
                className="font-mono text-sm"
              />
              <p className="text-xs text-gray-500 mt-1">
                Enter the Razorpay refund ID from the payment gateway
              </p>
            </div>
          )}

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes {newStatus === 'rejected' && <span className="text-red-500">*</span>}
            </label>
            <Textarea
              value={refundNotes}
              onChange={(e) => setRefundNotes(e.target.value)}
              placeholder={
                newStatus === 'rejected' 
                  ? "Please provide a reason for rejecting the refund..."
                  : "Optional notes about this refund status update..."
              }
              rows={3}
            />
          </div>

          {/* Error Message */}
          {error && (
            <div className="flex items-center gap-2 text-red-600 text-sm">
              <AlertCircle className="h-4 w-4" />
              {error}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? 'Updating...' : 'Update Status'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RefundStatusModal;
